<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.changneng.framework.frameworkbusiness.dao.LocalCheckItemMapper">
  <resultMap id="BaseResultMap" type="org.changneng.framework.frameworkbusiness.entity.LocalCheckItem">
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="CHECK_ITEM_NAME" jdbcType="VARCHAR" property="checkItemName" />
    <result column="CHECK_ITEM_RESULT" jdbcType="CLOB" property="checkItemResult" />
    <result column="TASK_ID" jdbcType="VARCHAR" property="taskId" />
    <result column="LOCAL_CHECK_ID" jdbcType="VARCHAR" property="localCheckId" />
    <result column="BEH_FACT" jdbcType="VARCHAR" property="behFact"/>
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithVARCHARs" type="org.changneng.framework.frameworkbusiness.entity.LocalCheckItem">
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="SCENE_ITEM_DATABASE_ID" jdbcType="VARCHAR" property="sceneItemDatabaseId"/>
 	 <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
   	<result column="UPDATE_TIME " jdbcType="TIMESTAMP" property="updateTime" />
   	<result column="LOCTION" jdbcType="DECIMAL" property="loction" />
   	<result column="BEH_ID" jdbcType="VARCHAR" property="behId"/>
   	<result column="BEH_FACT" jdbcType="VARCHAR" property="behFact"/>
   	<result column="CHECK_ITEM_STATUS" jdbcType="VARCHAR" property="checkItemStatus"/>
   	<result column="CHECK_ITEM_TYPE" jdbcType="VARCHAR" property="checkitemType"/>
 	<result column="IS_MUST" jdbcType="VARCHAR" property="isMust"/>
 	<result column="START_DATE" jdbcType="TIMESTAMP" property="startDate"/>
    <result column="END_DATE" jdbcType="TIMESTAMP" property="endDate"/>
    <result column="DATE_TYPE" jdbcType="VARCHAR" property="dateType"/>
    <result column="SCENE_SYS_ITEM_ID" jdbcType="VARCHAR" property="sceneSysItemId"/>
    <result column="FORM_TYPE" jdbcType="DECIMAL" property="formType"/>
    <result column="CONFIG_ITEM_ID" jdbcType="VARCHAR" property="configItemId"/>
    <result column="PROBLEM_DESC" jdbcType="CLOB" property="problemDesc"/>
  </resultMap>
  <sql id="Base_Column_List">
    ID,REMARK, CHECK_ITEM_NAME, CHECK_ITEM_RESULT, TASK_ID, LOCAL_CHECK_ID,SCENE_ITEM_DATABASE_ID,
    CREATE_TIME,UPDATE_TIME ,LOCTION,BEH_ID,CHECK_ITEM_STATUS,CHECK_ITEM_TYPE,IS_MUST,BEH_FACT,START_DATE,END_DATE,DATE_TYPE,SCENE_SYS_ITEM_ID,
    FORM_TYPE,CONFIG_ITEM_ID,PROBLEM_DESC
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="ResultMapWithVARCHARs">
    select
    <include refid="Base_Column_List" />
    from LOCAL_CHECK_ITEM
    where ID = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from LOCAL_CHECK_ITEM
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
   <delete id="deleteByLocalCheckId" parameterType="java.lang.String">
    delete from LOCAL_CHECK_ITEM
    where LOCAL_CHECK_ID = #{localCheckId,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="org.changneng.framework.frameworkbusiness.entity.LocalCheckItem">
    insert into LOCAL_CHECK_ITEM (ID, CHECK_ITEM_NAME, CHECK_ITEM_RESULT,
      TASK_ID, LOCAL_CHECK_ID, REMARK,SCENE_ITEM_DATABASE_ID,CREATE_TIME,
      UPDATE_TIME ,LOCTION,BEH_ID,CHECK_ITEM_STATUS,CHECK_ITEM_TYPE,IS_MUST,BEH_FACT,
      START_DATE,END_DATE,DATE_TYPE
      )
    values (#{id,jdbcType=VARCHAR}, #{checkItemName,jdbcType=VARCHAR}, #{checkItemResult,jdbcType=CLOB},
      #{taskId,jdbcType=VARCHAR}, #{localCheckId,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{sceneItemDatabaseId,jdbcType=VARCHAR}
      ,#{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP},#{loction,jdbcType=DECIMAL},
       #{behId,jdbcType=VARCHAR}, #{checkItemStatus,jdbcType=VARCHAR},
        #{checkitemType,jdbcType=VARCHAR},#{isMust,jdbcType=VARCHAR},
        #{behFact,jdbcType=VARCHAR} ,#{startDate,jdbcType=TIMESTAMP}
        ,#{endDate,jdbcType=TIMESTAMP}, #{dateType,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="org.changneng.framework.frameworkbusiness.entity.LocalCheckItem">
     <selectKey keyProperty="id" order="BEFORE" resultType="String">
  		select sys_guid() from dual
  	</selectKey>
    insert into LOCAL_CHECK_ITEM
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="checkItemName != null">
        CHECK_ITEM_NAME,
      </if>
      <if test="checkItemResult != null">
        CHECK_ITEM_RESULT,
      </if>
      <if test="taskId != null">
        TASK_ID,
      </if>
      <if test="localCheckId != null">
        LOCAL_CHECK_ID,
      </if>
      <if test="remark != null">
        REMARK,
      </if>
        <if test="sceneItemDatabaseId != null">
        SCENE_ITEM_DATABASE_ID,
      </if>
         <if test="createTime != null">
        CREATE_TIME,
      </if>
         <if test="updateTime != null">
        UPDATE_TIME ,
      </if>
           <if test="loction != null">
        LOCTION ,
      </if>
          <if test="behId != null">
        BEH_ID ,
      </if>
         <if test="checkItemStatus != null">
        CHECK_ITEM_STATUS ,
      </if>
         <if test="checkitemType != null">
        CHECK_ITEM_TYPE ,
      </if>
       <if test="isMust != null">
        IS_MUST ,
      </if>
        <if test="behFact != null">
        BEH_FACT ,
      </if>
        <if test="startDate != null">
        START_DATE ,
      </if>
        <if test="endDate != null">
        END_DATE ,
      </if>
         <if test="dateType != null">
        DATE_TYPE ,
      </if>
         <if test="sceneSysItemId != null">
        SCENE_SYS_ITEM_ID ,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="checkItemName != null">
        #{checkItemName,jdbcType=VARCHAR},
      </if>
      <if test="checkItemResult != null">
        #{checkItemResult,jdbcType=CLOB},
      </if>
      <if test="taskId != null">
        #{taskId,jdbcType=VARCHAR},
      </if>
      <if test="localCheckId != null">
        #{localCheckId,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
         <if test="sceneItemDatabaseId != null">
        #{sceneItemDatabaseId,jdbcType=VARCHAR},
      </if>
        <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
        <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
         <if test="loction != null">
        #{loction,jdbcType=DECIMAL},
      </if>
         <if test="behId != null">
        #{behId,jdbcType=VARCHAR},
      </if>
          <if test="checkItemStatus != null">
        #{checkItemStatus,jdbcType=VARCHAR},
      </if>
          <if test="checkitemType != null">
        #{checkitemType,jdbcType=VARCHAR},
      </if>
       <if test="isMust != null">
        #{isMust,jdbcType=VARCHAR},
      </if>
        <if test="behFact != null">
        #{behFact,jdbcType=VARCHAR},
      </if>
        <if test="startDate != null">
        #{startDate,jdbcType=TIMESTAMP},
      </if>
        <if test="endDate != null">
        #{endDate,jdbcType=TIMESTAMP},
      </if>
       <if test="dateType != null">
        #{dateType,jdbcType=VARCHAR},
      </if>
       <if test="sceneSysItemId != null">
        #{sceneSysItemId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="org.changneng.framework.frameworkbusiness.entity.LocalCheckItem">
    update LOCAL_CHECK_ITEM
    <set>
      <if test="checkItemName != null">
        CHECK_ITEM_NAME = #{checkItemName,jdbcType=VARCHAR},
      </if>
      <if test="checkItemResult != null">
        CHECK_ITEM_RESULT = #{checkItemResult,jdbcType=CLOB},
      </if>
      <if test="taskId != null">
        TASK_ID = #{taskId,jdbcType=VARCHAR},
      </if>
      <if test="localCheckId != null">
        LOCAL_CHECK_ID = #{localCheckId,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
       <if test="sceneItemDatabaseId != null">
        SCENE_ITEM_DATABASE_ID = #{sceneItemDatabaseId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME  = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="loction != null">
        LOCTION  = #{loction,jdbcType=DECIMAL},
      </if>
       <if test="behId != null">
        BEH_ID = #{behId,jdbcType=VARCHAR},
      </if>
        <if test="checkItemStatus != null">
        CHECK_ITEM_STATUS = #{checkItemStatus,jdbcType=VARCHAR},
      </if>
        <if test="checkitemType != null">
        CHECK_ITEM_TYPE = #{checkitemType,jdbcType=VARCHAR},
      </if>
       <if test="isMust != null">
        IS_MUST = #{isMust,jdbcType=VARCHAR},
      </if>
        <if test="behFact != null">
        BEH_FACT = #{behFact,jdbcType=VARCHAR},
      </if>
         <if test="startDate != null">
        START_DATE = #{startDate,jdbcType=TIMESTAMP},
      </if>
         <if test="endDate != null">
        END_DATE = #{endDate,jdbcType=TIMESTAMP},
      </if>
          <if test="dateType != null">
        DATE_TYPE = #{dateType,jdbcType=VARCHAR},
      </if>
      <if test="sceneSysItemId != null">
        SCENE_SYS_ITEM_ID = #{sceneSysItemId,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKeyWithVARCHARs" parameterType="org.changneng.framework.frameworkbusiness.entity.LocalCheckItem">
    update LOCAL_CHECK_ITEM
    set CHECK_ITEM_NAME = #{checkItemName,jdbcType=VARCHAR},
      CHECK_ITEM_RESULT = #{checkItemResult,jdbcType=CLOB},
      TASK_ID = #{taskId,jdbcType=VARCHAR},
      LOCAL_CHECK_ID = #{localCheckId,jdbcType=VARCHAR},
      REMARK = #{remark,jdbcType=VARCHAR},
      SCENE_ITEM_DATABASE_ID = #{sceneItemDatabaseId,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      UPDATE_TIME  = #{updateTime,jdbcType=TIMESTAMP},
      LOCTION  = #{loction,jdbcType=DECIMAL},
      BEH_ID = #{behId,jdbcType=VARCHAR},
      CHECK_ITEM_STATUS = #{checkItemStatus,jdbcType=VARCHAR},
      CHECK_ITEM_TYPE = #{checkitemType,jdbcType=VARCHAR},
      IS_MUST = #{isMust,jdbcType=VARCHAR},
      BEH_FACT = #{behFact,jdbcType=VARCHAR},
      START_DATE = #{startDate,jdbcType=TIMESTAMP},
      END_DATE = #{endDate,jdbcType=TIMESTAMP},
      DATE_TYPE = #{dateType,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="org.changneng.framework.frameworkbusiness.entity.LocalCheckItem">
    update LOCAL_CHECK_ITEM
    set CHECK_ITEM_NAME = #{checkItemName,jdbcType=VARCHAR},
      CHECK_ITEM_RESULT = #{checkItemResult,jdbcType=CLOB},
      TASK_ID = #{taskId,jdbcType=VARCHAR},
      LOCAL_CHECK_ID = #{localCheckId,jdbcType=VARCHAR},
      SCENE_ITEM_DATABASE_ID = #{sceneItemDatabaseId,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      UPDATE_TIME  = #{updateTime,jdbcType=TIMESTAMP},
      LOCTION  = #{loction,jdbcType=DECIMAL},
      BEH_ID = #{behId,jdbcType=VARCHAR},
      CHECK_ITEM_STATUS = #{checkItemStatus,jdbcType=VARCHAR},
      CHECK_ITEM_TYPE = #{checkitemType,jdbcType=VARCHAR},
      IS_MUST = #{isMust,jdbcType=VARCHAR},
      BEH_FACT = #{behFact,jdbcType=VARCHAR},
      START_DATE = #{startDate,jdbcType=TIMESTAMP},
      END_DATE = #{endDate,jdbcType=TIMESTAMP} ,
      DATE_TYPE = #{dateType,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <!-- getChickItemItem 根据任务的id和主键的id查询检查项-->
    <select id="getChickItemItem" parameterType="java.lang.String" resultMap="ResultMapWithVARCHARs">
    select
    <include refid="Base_Column_List" />
    from LOCAL_CHECK_ITEM
    where  1 =1
    <if test="taskId != null and taskId != ''">
       and  TASK_ID = #{taskId,jdbcType=VARCHAR}
      </if>
          <if test=" localChickId != null and localChickId != ''">
       and  LOCAL_CHECK_ID = #{localChickId,jdbcType=VARCHAR}
      </if>
      ORDER BY  to_number(loction) asc , CREATE_TIME asc
  </select>
  <!-- getChickItemItemByTaskId  根据任务的id查询检查项-->
    <select id="getChickItemItemByTaskId" parameterType="java.lang.String" resultMap="ResultMapWithVARCHARs">
    select
    <include refid="Base_Column_List" />
    from LOCAL_CHECK_ITEM
    where TASK_ID = #{taskId,jdbcType=VARCHAR}
    ORDER BY  to_number(loction) asc , CREATE_TIME asc
  </select>
  <!-- updateByLocalCheckItemId 修改查询项结果状态 -->
    <update id="updateByLocalCheckItemId" parameterType="org.changneng.framework.frameworkbusiness.entity.LocalCheckItem">
    update LOCAL_CHECK_ITEM
    set
      CHECK_ITEM_RESULT = #{status,jdbcType=CLOB}
    where ID = #{localCheckitemId,jdbcType=VARCHAR}
  </update>
  <!-- updateRemarkCheckItemById 修改备注信息 -->
      <update id="updateRemarkCheckItemById" parameterType="org.changneng.framework.frameworkbusiness.entity.LocalCheckItem">
    update LOCAL_CHECK_ITEM
    set
      REMARK = #{remarkCheckItemText,jdbcType=VARCHAR}
    where ID = #{localCheckitemId,jdbcType=VARCHAR}
  </update>
  <!-- updateChickItemByTaskId  保存时添加现场检查表主键id-->
    <update id="updateChickItemByTaskId" parameterType="org.changneng.framework.frameworkbusiness.entity.LocalCheckItem">
    update LOCAL_CHECK_ITEM
    set
      LOCAL_CHECK_ID = #{localCheckId,jdbcType=VARCHAR}
    where TASK_ID = #{taskId,jdbcType=VARCHAR}
  </update>

  <!--getChickItemItemList  -->
 <select id="getChickItemItemList" parameterType="java.lang.String" resultMap="ResultMapWithVARCHARs">
     select
    <include refid="Base_Column_List" />
    from LOCAL_CHECK_ITEM
    where TASK_ID = #{taskId,jdbcType=VARCHAR}
  </select>

  <!--  <select id="getSpecialItemItemList" parameterType="java.lang.String" resultMap="ResultMapWithVARCHARs">
     select
    <include refid="Base_Column_List" />
    from LOCAL_CHECK_ITEM
    where TASK_ID = #{taskId,jdbcType=VARCHAR}
    ORDER BY loction
  </select> -->


   <select id="getByTemplateType" parameterType="java.lang.String" resultMap="BaseResultMap">
    select sitd.id,ssi.LOCTION ,sitd.check_item_name,ssi.id as sceneSysItemId
	from scene_sys_item  ssi
	LEFT JOIN scene_item_database sitd ON ssi.scene_item_id = sitd.id
	where sys_modeler_id = (select id from  SCENE_SYSSPECAIL_MODEL	where template_type=#{TemplateType})
	ORDER BY to_number(ssi.LOCTION)
  </select>
   <select id="getInfoByid" parameterType="java.lang.String" resultMap="BaseResultMap">
    select sitd.id,ssi.LOCTION ,sitd.check_item_name,ssi.id as sceneSysItemId
	from scene_sys_item  ssi
	LEFT JOIN scene_item_database sitd ON ssi.scene_item_id = sitd.id
	where sys_modeler_id = (select id from  SCENE_SYSSPECAIL_MODEL	where id=#{id})
	ORDER BY to_number(ssi.LOCTION)
  </select>



   <select id="getSpecialItemItemList" parameterType="java.lang.String" resultMap="ResultMapWithVARCHARs">
     select
    <include refid="Base_Column_List" />
    from LOCAL_CHECK_ITEM
    where TASK_ID = #{taskId,jdbcType=VARCHAR}
    ORDER BY to_number(loction)
  </select>
  <!--saveLocalChickList批量保存检查项  -->
	  <insert id="saveLocalChickList" >
    insert all
    <foreach collection ="chickItemItem" item="item"  separator =" ">
      into  LOCAL_CHECK_ITEM ( ID,REMARK, CHECK_ITEM_NAME, CHECK_ITEM_RESULT,
      TASK_ID, LOCAL_CHECK_ID,SCENE_ITEM_DATABASE_ID,BEH_ID,CHECK_ITEM_STATUS,
      CHECK_ITEM_TYPE,IS_MUST,BEH_FACT,START_DATE,END_DATE,dateType)
    values
  	  (sys_guid(),
  	  #{item.remark},
  	  #{item.checkItemName},
      #{item.checkItemResult},
      #{item.taskId},
      #{item.localCheckId},
      #{item.sceneItemDatabaseId},
      #{item.behId},
      #{item.checkItemStatus},
      #{item.checkitemType},
      #{item.isMust},
      #{item.behFact},
      #{item.startDate},
      #{item.endDate},
      #{item.dateType}
       </foreach >
       select 1 from dual
       </insert>
       <!--getBehIdByCaseId根据案件的id查询关联任务下的违法行为id  -->
    <select id="getBehIdByCaseId" parameterType="java.lang.String" resultMap="ResultMapWithVARCHARs">
     select
     BEH_ID
     from LOCAL_CHECK_ITEM
     where
 	 TASK_ID IN (SELECT task_id from CASE_TASK where CASE_ID =#{caseId})
 	 and beh_id is not null;
  </select>

  <!----↓↓↓↓↓↓↓↓↓↓----->
  <!-- 查询环境监管一件事检查项 -->
  <select id="selectEnvSupervisionItems" parameterType="java.lang.String" resultMap="ResultMapWithVARCHARs">
    SELECT
    <include refid="Base_Column_List" />
    FROM LOCAL_CHECK_ITEM
    WHERE LOCAL_CHECK_ID = #{localCheckId,jdbcType=VARCHAR} AND FORM_TYPE = 1
    ORDER BY CONFIG_ITEM_ID
  </select>

  <!-- 根据本地检查ID和表单类型删除检查项 -->
  <delete id="deleteByLocalCheckIdAndFormType">
    DELETE FROM LOCAL_CHECK_ITEM
    WHERE LOCAL_CHECK_ID = #{localCheckId,jdbcType=VARCHAR} AND FORM_TYPE = #{formType,jdbcType=DECIMAL}
  </delete>

  <!-- 批量插入环境监管一件事检查项 -->
  <insert id="batchInsertEnvSupervisionItems">
    INSERT ALL
    <foreach collection="items" item="item" separator=" ">
      INTO LOCAL_CHECK_ITEM (
        ID, CHECK_ITEM_NAME, CHECK_ITEM_RESULT, LOCAL_CHECK_ID,
        FORM_TYPE, CONFIG_ITEM_ID, PROBLEM_DESC, CREATE_TIME, UPDATE_TIME
      ) VALUES (
        SYS_GUID(), #{item.itemName,jdbcType=VARCHAR}, #{item.result,jdbcType=VARCHAR},
        #{item.localCheckId,jdbcType=VARCHAR}, 1, #{item.configItemId,jdbcType=VARCHAR},
        #{item.problemDesc,jdbcType=CLOB}, SYSDATE, SYSDATE
      )
    </foreach>
    SELECT 1 FROM DUAL
  </insert>
  <!------------↑↑↑↑↑↑----->
</mapper>
